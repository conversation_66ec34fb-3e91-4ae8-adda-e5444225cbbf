# 台灣人工智慧政策 (Taiwan AI Policy)

## 第一章 總則

### 1.1 政策目的
本政策旨在建立 Deloitte Touche Tohmatsu Limited (DTTL) 台灣成員公司人工智慧 (AI) 系統之治理架構，確保 AI 技術的負責任使用，並符合台灣相關法規及國際最佳實務標準。

### 1.2 適用範圍
本政策適用於：
- 所有台灣成員公司及其員工
- 所有涉及 AI 系統開發、部署、使用或維護的活動
- 包含生成式 AI、機器學習、深度學習等各類 AI 技術應用
- 第三方 AI 服務的採購與使用

### 1.3 政策原則
本政策基於以下核心原則：
- **可信任性**：建立值得信賴的 AI 系統
- **透明度**：確保 AI 決策過程可理解與可稽核
- **公平性**：避免偏見與歧視
- **隱私保護**：尊重個人資料隱私權
- **安全性**：確保系統安全與資料保護
- **當責性**：明確責任歸屬與問責機制

## 第二章 定義與分類

### 2.1 AI 系統定義
人工智慧系統係指基於機器學習、深度學習或其他演算法技術，能夠接收輸入資料並產生預測、建議、決策或其他輸出結果，進而影響實體或虛擬環境的系統。

### 2.2 AI 系統分類
依據風險等級將 AI 系統分為四類：

#### 2.2.1 最小風險 AI 系統
- 基本資料處理與分析工具
- 簡單的自動化流程
- 無涉及個人資料或重要決策的應用

#### 2.2.2 有限風險 AI 系統
- 客戶服務聊天機器人
- 內部文件摘要工具
- 一般性資料分析應用

#### 2.2.3 高風險 AI 系統
- 涉及人力資源決策的系統
- 客戶信用評估或風險評級
- 涉及個人敏感資料處理的應用

#### 2.2.4 不可接受風險 AI 系統
- 可能造成人身傷害的系統
- 違反法律或倫理標準的應用
- 侵犯基本人權的技術

## 第三章 治理架構

### 3.1 組織架構

#### 3.1.1 AI 治理委員會
- **組成**：由高階主管、技術專家、法務、風險管理及合規人員組成
- **職責**：制定 AI 策略、審核高風險 AI 專案、監督政策執行
- **會議頻率**：每季召開一次，必要時得召開臨時會議

#### 3.1.2 AI 風險長 (Chief AI Risk Officer)
- **任命**：由執行長指派具備相關專業背景之高階主管擔任
- **職責**：
  - 監督 AI 風險管理制度的建立與執行
  - 向治理委員會報告 AI 風險狀況
  - 協調跨部門 AI 相關事務

#### 3.1.3 AI 專案負責人
- **指派**：每個 AI 專案應指派專責負責人
- **職責**：
  - 確保專案符合本政策要求
  - 執行風險評估與管控措施
  - 定期回報專案狀況

### 3.2 角色與責任

#### 3.2.1 高階管理層
- 確保充分的資源投入
- 建立 AI 治理文化
- 監督政策執行成效

#### 3.2.2 業務單位
- 識別 AI 應用需求與機會
- 配合執行風險評估
- 確保業務流程符合政策要求

#### 3.2.3 技術團隊
- 負責 AI 系統的技術實作
- 執行技術風險評估
- 確保系統安全與效能

#### 3.2.4 法務與合規
- 提供法規遵循指導
- 審核合約與協議
- 處理法律風險議題

## 第四章 風險管理制度

### 4.1 風險評估流程

#### 4.1.1 初步風險評估
所有 AI 專案在啟動前應完成初步風險評估，包括：
- **技術風險**：演算法偏見、模型準確性、系統穩定性
- **資料風險**：資料品質、隱私保護、資料安全
- **營運風險**：業務流程影響、人力資源配置
- **法規風險**：法律合規性、監管要求
- **聲譽風險**：公眾觀感、媒體關注

#### 4.1.2 詳細風險評估
高風險 AI 系統應進行詳細風險評估：
- 委託第三方專業機構執行
- 包含技術稽核與倫理審查
- 提出具體風險控制建議
- 建立持續監控機制

### 4.2 風險控制措施

#### 4.2.1 技術控制
- **模型驗證**：建立模型測試與驗證程序
- **偏見檢測**：定期檢測演算法偏見
- **效能監控**：持續監控系統效能指標
- **安全防護**：實施資訊安全防護措施

#### 4.2.2 流程控制
- **審核機制**：建立多層級審核程序
- **變更管理**：制定系統變更管控流程
- **文件管理**：維護完整的技術文件
- **備援計畫**：建立系統故障應變機制

#### 4.2.3 人員控制
- **權限管理**：實施最小權限原則
- **教育訓練**：定期辦理 AI 相關訓練
- **行為準則**：制定 AI 使用行為規範
- **監督機制**：建立內部監督制度

### 4.3 事件管理

#### 4.3.1 事件分類
- **第一級**：系統故障或效能異常
- **第二級**：資料洩露或隱私侵犯
- **第三級**：演算法偏見或歧視事件
- **第四級**：重大安全事故或法規違反

#### 4.3.2 應變程序
1. **立即回應**：24小時內啟動應變機制
2. **損害控制**：採取必要措施降低影響
3. **原因調查**：深入分析事件成因
4. **改善措施**：制定預防再發生的措施
5. **經驗學習**：更新政策與程序

## 第五章 資料治理

### 5.1 資料管理原則

#### 5.1.1 資料品質
- 確保訓練資料的準確性與完整性
- 建立資料品質檢核機制
- 定期更新與維護資料集
- 記錄資料來源與處理歷程

#### 5.1.2 資料隱私
- 遵循個人資料保護法規定
- 實施資料去識別化處理
- 建立資料存取權限控制
- 提供資料主體權利保障

#### 5.1.3 資料安全
- 實施資料加密保護
- 建立資料備份與復原機制
- 監控資料存取行為
- 防範資料外洩風險

### 5.2 資料使用規範

#### 5.2.1 合法使用
- 確保資料取得的合法性
- 遵循資料使用目的限制
- 取得必要的同意或授權
- 符合跨境資料傳輸規定

#### 5.2.2 倫理使用
- 避免使用可能造成歧視的資料
- 尊重資料主體的權益
- 考量資料使用的社會影響
- 建立倫理審查機制

## 第六章 採購與合作

### 6.1 第三方 AI 服務採購

#### 6.1.1 供應商評估
- **技術能力**：評估技術成熟度與可靠性
- **安全標準**：確認資訊安全防護措施
- **合規狀況**：檢視法規遵循情形
- **服務品質**：評估服務水準與支援能力

#### 6.1.2 合約管理
- 明確定義服務範圍與品質標準
- 規範資料處理與隱私保護要求
- 建立服務水準協議 (SLA)
- 制定合約終止與資料返還程序

### 6.2 合作夥伴管理

#### 6.2.1 合作評估
- 評估合作夥伴的 AI 治理能力
- 確認雙方政策與標準的一致性
- 建立共同的風險管理機制
- 制定資訊分享與保護協議

#### 6.2.2 持續監督
- 定期檢視合作關係
- 監控合作專案的執行狀況
- 評估合作效益與風險
- 適時調整合作策略

## 第七章 教育訓練與意識提升

### 7.1 訓練計畫

#### 7.1.1 基礎訓練
**對象**：全體員工
**內容**：
- AI 基本概念與應用
- 本政策重點說明
- AI 使用倫理與責任
- 資料隱私保護意識

**頻率**：每年至少一次，新進員工於到職三個月內完成

#### 7.1.2 專業訓練
**對象**：AI 相關工作人員
**內容**：
- AI 技術深度知識
- 風險評估與管理
- 法規遵循要求
- 最佳實務案例

**頻率**：每年至少兩次，並配合技術發展適時更新

#### 7.1.3 管理訓練
**對象**：主管與決策人員
**內容**：
- AI 治理策略
- 風險管理決策
- 法律責任認知
- 危機處理能力

**頻率**：每年至少一次，重大政策變更時加強訓練

### 7.2 意識提升活動

#### 7.2.1 內部宣導
- 定期發布 AI 相關資訊
- 舉辦專題講座與研討會
- 分享最佳實務案例
- 建立內部交流平台

#### 7.2.2 外部參與
- 參與產業 AI 治理倡議
- 與學術機構合作研究
- 參加國際會議與論壇
- 分享治理經驗與成果

## 第八章 監督與稽核

### 8.1 內部稽核

#### 8.1.1 稽核範圍
- AI 政策執行情形
- 風險管理制度有效性
- 資料治理合規狀況
- 教育訓練實施成效

#### 8.1.2 稽核頻率
- **例行稽核**：每年至少一次全面稽核
- **專案稽核**：高風險專案完成後進行
- **突擊稽核**：發現異常時立即執行
- **追蹤稽核**：缺失改善後的追蹤檢查

#### 8.1.3 稽核程序
1. **稽核計畫**：制定年度稽核計畫
2. **稽核執行**：依計畫執行稽核作業
3. **結果報告**：撰寫稽核報告與建議
4. **缺失改善**：追蹤缺失改善情形
5. **成效評估**：評估改善措施有效性

### 8.2 外部監督

#### 8.2.1 監管機關
- 配合主管機關檢查
- 定期提交合規報告
- 回應監管詢問與要求
- 參與政策制定諮詢

#### 8.2.2 第三方驗證
- 委託專業機構進行獨立評估
- 取得相關認證與標章
- 參與同業評比與標竿學習
- 接受客戶與利害關係人監督

## 第九章 違規處理

### 9.1 違規認定

#### 9.1.1 違規類型
- **輕微違規**：未依程序執行但無重大影響
- **一般違規**：違反政策規定造成一定影響
- **嚴重違規**：造成重大損失或法律風險
- **重大違規**：涉及刑事責任或重大聲譽損害

#### 9.1.2 調查程序
1. **事件通報**：發現違規立即通報
2. **初步調查**：24小時內啟動調查
3. **深度調查**：必要時委託外部專家
4. **責任認定**：確認違規事實與責任歸屬
5. **處理決定**：依規定給予適當處分

### 9.2 處分措施

#### 9.2.1 行政處分
- **口頭警告**：輕微違規初犯
- **書面警告**：一般違規或再犯
- **記過處分**：嚴重違規或屢犯
- **降職減薪**：重大違規造成嚴重後果
- **解僱處分**：最嚴重違規或涉及犯罪

#### 9.2.2 補救措施
- 要求立即停止違規行為
- 採取必要的損害控制措施
- 進行額外的教育訓練
- 加強監督與管控機制
- 公開道歉或澄清說明

### 9.3 申訴機制

#### 9.3.1 申訴程序
- 當事人得於處分通知後15日內提出申訴
- 由 AI 治理委員會組成申訴審議小組
- 申訴審議小組應於30日內完成審議
- 審議結果為最終決定

#### 9.3.2 保護措施
- 保護申訴人與檢舉人身分
- 禁止對申訴人進行報復
- 提供必要的法律協助
- 建立公正透明的審議程序

## 第十章 政策實施與檢討

### 10.1 實施計畫

#### 10.1.1 分階段實施
**第一階段（前3個月）**：
- 成立 AI 治理委員會
- 任命 AI 風險長
- 完成現有 AI 系統盤點
- 啟動基礎教育訓練

**第二階段（3-6個月）**：
- 建立風險評估機制
- 制定詳細作業程序
- 完成高風險系統評估
- 強化資料治理制度

**第三階段（6-12個月）**：
- 全面實施監督稽核
- 建立事件管理機制
- 完善教育訓練體系
- 建立績效評估指標

#### 10.1.2 資源配置
- **人力資源**：指派專責人員負責政策執行
- **財務資源**：編列充足預算支應相關費用
- **技術資源**：建置必要的系統與工具
- **外部資源**：聘請專業顧問提供協助

### 10.2 績效評估

#### 10.2.1 評估指標
**量化指標**：
- AI 系統風險評估完成率
- 教育訓練參與率與通過率
- 事件發生次數與處理時效
- 稽核缺失數量與改善率

**質化指標**：
- 員工 AI 意識與能力提升
- 客戶與利害關係人滿意度
- 監管機關評價與回饋
- 產業聲譽與競爭地位

#### 10.2.2 評估方法
- **定期評估**：每季進行績效檢討
- **年度評估**：每年進行全面評估
- **專案評估**：重大專案完成後評估
- **外部評估**：委託第三方進行獨立評估

### 10.3 政策檢討與更新

#### 10.3.1 檢討機制
- **定期檢討**：每年至少檢討一次
- **適時檢討**：法規變更或重大事件發生時
- **主動檢討**：技術發展或最佳實務更新時
- **被動檢討**：監管機關要求或外部建議時

#### 10.3.2 更新程序
1. **需求評估**：分析政策更新需求
2. **草案研擬**：起草政策修正版本
3. **內部諮詢**：徵詢相關部門意見
4. **外部諮詢**：必要時諮詢外部專家
5. **核准發布**：經治理委員會核准後發布

## 附錄

### 附錄一：可信任 AI 核心原則

#### A1.1 負責任 (Accountability)
- **明確責任歸屬**：每個 AI 系統都應有明確的負責人
- **決策可追溯**：AI 決策過程應可追蹤與稽核
- **後果承擔**：對 AI 系統造成的後果承擔相應責任
- **持續改善**：基於回饋持續改善系統效能

#### A1.2 公平與公正 (Fairness)
- **避免偏見**：識別並消除演算法中的不當偏見
- **平等對待**：確保不同群體獲得公平對待
- **包容設計**：考量多元族群的需求與特性
- **救濟機制**：提供受不公平對待者的救濟途徑

#### A1.3 隱私保護 (Privacy)
- **資料最小化**：僅收集必要的個人資料
- **目的限制**：資料使用應符合收集目的
- **同意機制**：取得適當的資料使用同意
- **權利保障**：保障資料主體的各項權利

#### A1.4 可靠性 (Reliability)
- **系統穩定**：確保系統穩定運行
- **效能監控**：持續監控系統效能表現
- **故障復原**：建立快速故障復原機制
- **品質保證**：實施嚴格的品質管控程序

#### A1.5 安全性 (Security)
- **資訊安全**：保護系統免受網路攻擊
- **實體安全**：確保硬體設備的實體安全
- **存取控制**：實施嚴格的存取權限管理
- **威脅防護**：建立威脅偵測與防護機制

#### A1.6 透明度 (Transparency)
- **演算法透明**：適度揭露演算法運作原理
- **決策解釋**：提供 AI 決策的合理解釋
- **資訊公開**：公開相關政策與程序資訊
- **溝通機制**：建立與利害關係人的溝通管道

### 附錄二：風險評估檢核表

#### A2.1 技術風險檢核
□ 演算法是否經過充分測試與驗證？
□ 是否存在已知的演算法偏見問題？
□ 模型準確性是否符合業務需求？
□ 系統效能是否穩定可靠？
□ 是否建立適當的監控機制？

#### A2.2 資料風險檢核
□ 訓練資料來源是否合法？
□ 資料品質是否符合要求？
□ 是否涉及個人敏感資料？
□ 資料安全防護是否充足？
□ 是否符合資料保護法規？

#### A2.3 營運風險檢核
□ 對現有業務流程的影響程度？
□ 人力資源配置是否充足？
□ 是否具備必要的技術能力？
□ 變更管理程序是否完善？
□ 備援計畫是否充分？

#### A2.4 法規風險檢核
□ 是否符合相關法律法規？
□ 是否需要特殊許可或核准？
□ 跨境資料傳輸是否合規？
□ 智慧財產權是否清楚？
□ 契約條款是否完備？

### 附錄三：相關法規與標準

#### A3.1 台灣相關法規
- 個人資料保護法
- 資通安全管理法
- 公司法
- 消費者保護法
- 公平交易法

#### A3.2 國際標準與指引
- ISO/IEC 23053:2022 AI 風險管理架構
- IEEE 2857-2021 AI 工程標準
- EU AI Act 歐盟人工智慧法案
- NIST AI 風險管理架構
- OECD AI 原則

#### A3.3 產業最佳實務
- Partnership on AI 最佳實務指引
- AI Ethics Guidelines Global Inventory
- Responsible AI Practices
- Trustworthy AI Assessment List

---

## 政策生效資訊

**制定單位**：Deloitte Taiwan AI 治理委員會
**核准日期**：2024年12月____日
**生效日期**：2025年1月1日
**版本編號**：V1.0
**下次檢討日期**：2025年12月31日

**聯絡資訊**：
AI 風險長辦公室
電話：(02) xxxx-xxxx
電子郵件：<EMAIL>

---

*本政策如有疑義，以中文版本為準。*